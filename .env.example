# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/oms_db

# Redis Configuration for Stock Management
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Firebase Configuration
FIREBASE_CONFIG_PATH=app/firebase-sample.json
SKIP_FIREBASE_AUTH=true

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Application Configuration
LOG_LEVEL=INFO
