"""init

Revision ID: a6f69c213aa0
Revises: 
Create Date: 2025-06-29 13:21:08.286924

"""
from typing import Sequence, Union
from alembic import op


# revision identifiers, used by Alembic.
revision: str = 'a6f69c213aa0'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("""
        CREATE TABLE IF NOT EXISTS orders (
            id SERIAL PRIMARY KEY,
            random_prefix VARCHAR(4) NOT NULL,
            order_id VARCHAR(50) GENERATED ALWAYS AS (random_prefix || '+' || id::TEXT) STORED UNIQUE,
            customer_id VARCHAR(50) NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            facility_id VARCHAR(50) NOT NULL,
            facility_name VARCHAR(100) NOT NULL,
            status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'confirmed', 'delivered', 'cancelled')),
            total_amount DECIMAL(10,2) NOT NULL,
            eta TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );"""
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.execute("DROP TABLE IF EXISTS orders;")
