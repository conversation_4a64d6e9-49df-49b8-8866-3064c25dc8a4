"""add the order_items table indexes

Revision ID: a17cc2efa427
Revises: 86c680491d7f
Create Date: 2025-06-29 13:38:37.837408

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a17cc2efa427'
down_revision: Union[str, Sequence[str], None] = '86c680491d7f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("""CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);""")
    op.execute("""CREATE INDEX IF NOT EXISTS idx_order_items_sku ON order_items(sku);""")


def downgrade() -> None:
    """Downgrade schema."""
    op.execute("""DROP INDEX idx_order_items_order_id;""")
    op.execute("""DROP INDEX idx_order_items_sku;""")
