"""add the order table indexes

Revision ID: 86c680491d7f
Revises: 753568d32afa
Create Date: 2025-06-29 13:36:18.209297

"""
from typing import Sequence, Union
from alembic import op


# revision identifiers, used by Alembic.
revision: str = '86c680491d7f'
down_revision: Union[str, Sequence[str], None] = '753568d32afa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("""CREATE INDEX idx_customer_id ON orders(customer_id);""")
    op.execute("""CREATE INDEX idx_facility_id ON orders(facility_id);""")
    op.execute("""CREATE INDEX idx_status ON orders(status);""")
    op.execute("""CREATE INDEX idx_eta ON orders(eta);""")
    op.execute("""CREATE INDEX idx_created_at ON orders(created_at);""")
    op.execute("""CREATE INDEX idx_updated_at ON orders(updated_at);""")


def downgrade() -> None:
    """Downgrade schema."""
    op.execute("""DROP INDEX idx_customer_id;""")
    op.execute("""DROP INDEX idx_facility_id;""")
    op.execute("""DROP INDEX idx_status;""")
    op.execute("""DROP INDEX idx_eta;""")
    op.execute("""DROP INDEX idx_created_at;""")
    op.execute("""DROP INDEX idx_updated_at;""")
