from psycopg_pool import ConnectionPool
from contextlib import asynccontextmanager
import os
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DATABASE_URL = os.getenv("DATABASE_URL")

# Single database pool
db_pool = None

def get_db_pool():
    """Get single database connection pool"""
    global db_pool
    if db_pool is None:
        try:
            db_pool = ConnectionPool(
                conninfo=DATABASE_URL,
                min_size=5,
                max_size=50,
                kwargs={
                    "keepalives_idle": 600,
                    "keepalives_interval": 30,
                    "keepalives_count": 3
                }
            )
            logger.info("Database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    return db_pool

@asynccontextmanager
async def get_db():
    """Get database connection (for both read and write operations)"""
    pool = get_db_pool()
    try:
        with pool.connection() as conn:
            conn.autocommit = False
            logger.debug("Database connection acquired")
            yield conn
    except Exception as e:
        logger.error(f"Error with database connection: {e}")
        raise

@asynccontextmanager
async def get_db_readonly():
    """Get database connection optimized for read-only operations"""
    pool = get_db_pool()
    try:
        with pool.connection() as conn:
            conn.autocommit = True
            with conn.cursor() as cursor:
                cursor.execute("SET TRANSACTION READ ONLY")
            logger.debug("Read-only database connection acquired")
            yield conn
    except Exception as e:
        logger.error(f"Error with read-only database connection: {e}")
        raise

def close_db_pool():
    """Close database connection pool - useful for cleanup"""
    global db_pool

    if db_pool:
        try:
            db_pool.close()
            logger.info("Database pool closed")
        except Exception as e:
            logger.error(f"Error closing database pool: {e}")
        finally:
            db_pool = None

# Backward compatibility aliases (for easier migration)
get_write_db = get_db
get_read_db = get_db_readonly
close_all_pools = close_db_pool