from app.models.orders import OrderCreate
import os


class OrderCreateValidator:
    def __init__(self, order: OrderCreate = None, user_id: str = None):
        self.order = order
        self.user_id = user_id

    def validate(self):
        self.validate_user_id_customer_id()

    def validate_user_id_customer_id(self):
        # Skip validation if Firebase auth is disabled for testing
        skip_auth = os.getenv("SKIP_FIREBASE_AUTH", "false").lower() == "true"

        if skip_auth:
            # In testing mode, allow any customer_id
            return

        # In production mode, enforce user_id == customer_id
        if self.user_id != self.order.customer_id:
            raise ValueError("User ID and Customer ID do not match")
    
    def validate_page_size(self, limit: int = 20, offset: int = 0):
        # max difference between limit and offset should be 20
        if abs(limit - offset) > 20:
            raise ValueError("Pagination size should be less than 20")
