from typing import Dict, Optional
import logging
import random
import string
from datetime import datetime

from psycopg.rows import dict_row

logger = logging.getLogger(__name__)

def generate_random_prefix() -> str:
    """Generate a random 4-character alphanumeric string for order ID prefix.

    Returns:
        str: A 4-character string containing uppercase letters and digits (e.g., 'A2K4', '489K', 'X7B9')
    """
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=4))

class OrderCommandService:
    """Service for handling order commands (Create, Update, Cancel) with single database"""

    def __init__(self, db_conn=None):
        self.db_conn = db_conn

    async def create_order(self, order_data: Dict, db_conn=None) -> Dict:
        """Create order - Direct write to database with auto-generated order_id"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for create operation")

        try:
            logger.info(f"Creating order for customer {order_data['customer_id']}")

            # Validate required fields
            required_fields = ['customer_id', 'customer_name', 'facility_id', 'facility_name', 'status', 'total_amount']
            for field in required_fields:
                if field not in order_data or not order_data[field]:
                    raise ValueError(f"Missing required field: {field}")


            # Direct database write with transaction
            with conn.cursor() as cursor:
                try:
                    # Calculate ETA (24 hours from now as default)
                    from datetime import datetime, timedelta
                    eta = datetime.now() + timedelta(hours=24)

                    # Generate random prefix for order ID
                    random_prefix = generate_random_prefix()

                    # Insert order - order_id will be auto-generated by PostgreSQL using the random_prefix
                    order_query = """
                        INSERT INTO orders (
                            random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        RETURNING order_id, id
                    """

                    order_values = (
                        random_prefix,
                        order_data['customer_id'],
                        order_data['customer_name'],
                        order_data['facility_id'],
                        order_data['facility_name'],
                        order_data['status'],
                        order_data['total_amount'],
                        eta
                    )

                    cursor.execute(order_query, order_values)
                    result_row = cursor.fetchone()
                    generated_order_id = result_row[0]

                    # Insert order items
                    if order_data.get('items'):
                        item_query = """
                            INSERT INTO order_items (
                                order_id, sku, quantity, unit_price, sale_price, status
                            ) VALUES (%s, %s, %s, %s, %s, %s)
                        """

                        for item in order_data['items']:
                            item_values = (
                                generated_order_id,
                                item['sku'],
                                item['quantity'],
                                item['unit_price'],
                                item['sale_price'],
                                order_data['status']  # Set item status same as order status
                            )
                            cursor.execute(item_query, item_values)

                    conn.commit()
                    logger.info(f"Order {generated_order_id} created successfully in database")

                    return {
                        "success": True,
                        "message": "Order created successfully",
                        "order_id": generated_order_id,
                        "eta": eta.isoformat()
                    }

                except Exception as db_error:
                    conn.rollback()
                    logger.error(f"Database error creating order: {db_error}")
                    raise

        except Exception as e:
            logger.error(f"Failed to create order: {e}")
            raise

    async def update_order_status(self, order_id: str, status: str, db_conn=None) -> Dict:
        """Update order status in both orders and order_items tables"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Update order status
                cursor.execute("""
                    UPDATE orders
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                # Update order items status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                conn.commit()
                logger.info(f"Order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Order status updated to {status}",
                    "order_id": order_id
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update order status {order_id}: {e}")
            raise

    async def update_item_status(self, order_id: str, sku: str, status: str, db_conn=None) -> Dict:
        """Update status of a specific item within an order"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Check if item exists in the order
                cursor.execute("""
                    SELECT id FROM order_items
                    WHERE order_id = %s AND sku = %s
                """, (order_id, sku))

                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }

                # Update specific item status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s AND sku = %s
                """, (status, order_id, sku))

                conn.commit()
                logger.info(f"Item {sku} in order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Item '{sku}' status updated to '{status}'",
                    "order_id": order_id,
                    "sku": sku,
                    "status": status
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update item status {order_id}/{sku}: {e}")
            raise

class OrderQueryService:
    """Service for handling order queries (Read operations) from single database"""

    def __init__(self, db_conn):
        self.db_conn = db_conn

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get single order by order_id from database with complete details"""
        if not self.db_conn:
            raise ValueError("Database is required for queries")


        # Use dict_row row factory so we can access columns by name
        with self.db_conn.cursor(row_factory=dict_row) as cursor:
            sql = """
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at,
                       oi.sku, oi.quantity, oi.unit_price, oi.sale_price, oi.status AS item_status
                FROM orders o
                LEFT JOIN order_items oi ON oi.order_id = o.order_id
                WHERE o.order_id = %s
            """
            cursor.execute(sql, (order_id,))
            rows = cursor.fetchall()
            if not rows:
                return None

            header = rows[0]
            items = []

            for r in rows:
                if r["sku"] is not None:
                    item_dict = {
                        "sku": r["sku"],
                        "quantity": r["quantity"],
                        "unit_price": float(r["unit_price"]) if r["unit_price"] is not None else None,
                        "sale_price": float(r["sale_price"]) if r["sale_price"] is not None else None,
                        "status": r["item_status"],
                    }
                    items.append(item_dict)

            return {
                "order_id": header["order_id"],
                "customer_id": header["customer_id"],
                "customer_name": header["customer_name"],
                "facility_id": header["facility_id"],
                "facility_name": header["facility_name"],
                "status": header["status"],
                "total_amount": float(header["total_amount"]),
                "eta": header["eta"],
                "created_at": header["created_at"],
                "updated_at": header["updated_at"],
                "items": items,
            }
    
    def get_all_orders(self, customer_id: str, limit: int = 20, offset: int = 0) -> list[Dict]:
        """Return paginated orders for the given customer (newest first)."""
        if not self.db_conn:
            raise ValueError("Database is required for queries")

        # Use dict_row row factory so we can access columns by name
        with self.db_conn.cursor(row_factory=dict_row) as cursor:
            sql = """
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at,
                       oi.sku, oi.quantity, oi.unit_price, oi.sale_price, oi.status AS item_status
                FROM orders o
                LEFT JOIN order_items oi ON oi.order_id = o.order_id
                WHERE o.customer_id = %s
                ORDER BY o.created_at DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(sql, (customer_id, limit, offset))
            rows = cursor.fetchall()
            if not rows:
                return []

            orders_map: Dict[str, Dict] = {}
            for r in rows:
                oid = r["order_id"]
                order_entry = orders_map.setdefault(
                    oid,
                    {
                        "order_id": r["order_id"],
                        "customer_id": r["customer_id"],
                        "customer_name": r["customer_name"],
                        "facility_id": r["facility_id"],
                        "facility_name": r["facility_name"],
                        "status": r["status"],
                        "total_amount": float(r["total_amount"]) if r["total_amount"] is not None else None,
                        "eta": r["eta"],
                        "created_at": r["created_at"],
                        "updated_at": r["updated_at"],
                        "items": [],
                    },
                )

                if r["sku"] is not None:
                    order_entry["items"].append(
                        {
                            "sku": r["sku"],
                            "quantity": r["quantity"],
                            "unit_price": float(r["unit_price"]) if r["unit_price"] is not None else None,
                            "sale_price": float(r["sale_price"]) if r["sale_price"] is not None else None,
                            "status": r["item_status"],
                        }
                    )

            return list(orders_map.values())
