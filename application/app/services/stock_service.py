import redis
import json
import logging
from typing import Dict, List, Optional, Tuple
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class StockService:
    """Redis-based stock management service for real-time inventory tracking"""
    
    def __init__(self):
        self.redis_host = os.getenv("REDIS_HOST", "localhost")
        self.redis_port = int(os.getenv("REDIS_PORT", 6379))
        self.redis_db = int(os.getenv("REDIS_DB", 0))
        self.redis_password = os.getenv("REDIS_PASSWORD", None)
        
        # Redis key prefixes
        self.STOCK_KEY_PREFIX = "stock:"
        self.RESERVED_KEY_PREFIX = "reserved:"
        self.RESERVATION_TIMEOUT = 300  # 5 minutes reservation timeout
        
        self._redis_client = None
    
    def get_redis_client(self) -> redis.Redis:
        """Get Redis client with connection pooling"""
        if self._redis_client is None:
            try:
                self._redis_client = redis.Redis(
                    host=self.redis_host,
                    port=self.redis_port,
                    db=self.redis_db,
                    password=self.redis_password,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                # Test connection
                self._redis_client.ping()
                logger.info("Redis connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise
        return self._redis_client
    
    def _get_stock_key(self, sku: str, facility_id: str) -> str:
        """Generate Redis key for stock data"""
        return f"{self.STOCK_KEY_PREFIX}{facility_id}:{sku}"
    
    def _get_reserved_key(self, sku: str, facility_id: str) -> str:
        """Generate Redis key for reserved stock data"""
        return f"{self.RESERVED_KEY_PREFIX}{facility_id}:{sku}"
    
    def get_available_stock(self, sku: str, facility_id: str) -> int:
        """Get available stock for a SKU at a facility"""
        try:
            redis_client = self.get_redis_client()
            stock_key = self._get_stock_key(sku, facility_id)
            reserved_key = self._get_reserved_key(sku, facility_id)
            
            # Get total stock
            total_stock = redis_client.get(stock_key)
            if total_stock is None:
                logger.warning(f"No stock data found for SKU {sku} at facility {facility_id}")
                return 0
            
            total_stock = int(total_stock)
            
            # Get reserved stock
            reserved_stock = redis_client.get(reserved_key)
            reserved_stock = int(reserved_stock) if reserved_stock else 0
            
            available_stock = max(0, total_stock - reserved_stock)
            
            logger.debug(f"SKU {sku} at {facility_id}: Total={total_stock}, Reserved={reserved_stock}, Available={available_stock}")
            return available_stock
            
        except Exception as e:
            logger.error(f"Error getting stock for SKU {sku} at facility {facility_id}: {e}")
            raise
    
    def check_stock_availability(self, items: List[Dict], facility_id: str) -> Tuple[bool, List[Dict]]:
        """
        Check stock availability for multiple items
        
        Args:
            items: List of items with 'sku' and 'quantity' keys
            facility_id: Facility ID to check stock for
            
        Returns:
            Tuple of (is_available, unavailable_items)
        """
        try:
            unavailable_items = []
            
            for item in items:
                sku = item['sku']
                requested_qty = item['quantity']
                
                available_stock = self.get_available_stock(sku, facility_id)
                
                if available_stock < requested_qty:
                    unavailable_items.append({
                        'sku': sku,
                        'requested_quantity': requested_qty,
                        'available_quantity': available_stock,
                        'shortage': requested_qty - available_stock
                    })
            
            is_available = len(unavailable_items) == 0
            
            if is_available:
                logger.info(f"Stock check passed for {len(items)} items at facility {facility_id}")
            else:
                logger.warning(f"Stock check failed for {len(unavailable_items)} items at facility {facility_id}")
            
            return is_available, unavailable_items
            
        except Exception as e:
            logger.error(f"Error checking stock availability: {e}")
            raise
    
    def reserve_stock(self, items: List[Dict], facility_id: str, reservation_id: str) -> bool:
        """
        Reserve stock for order items
        
        Args:
            items: List of items with 'sku' and 'quantity' keys
            facility_id: Facility ID
            reservation_id: Unique reservation identifier (usually order_id)
            
        Returns:
            bool: True if reservation successful, False otherwise
        """
        try:
            redis_client = self.get_redis_client()
            pipe = redis_client.pipeline()
            
            # First, check if all items are still available
            is_available, unavailable_items = self.check_stock_availability(items, facility_id)
            
            if not is_available:
                logger.error(f"Cannot reserve stock - items unavailable: {unavailable_items}")
                return False
            
            # Reserve stock for each item
            for item in items:
                sku = item['sku']
                quantity = item['quantity']
                reserved_key = self._get_reserved_key(sku, facility_id)
                
                # Increment reserved stock
                pipe.incrby(reserved_key, quantity)
                # Set expiration for reservation
                pipe.expire(reserved_key, self.RESERVATION_TIMEOUT)
                
                # Store reservation details for tracking
                reservation_data = {
                    'reservation_id': reservation_id,
                    'sku': sku,
                    'quantity': quantity,
                    'facility_id': facility_id,
                    'timestamp': str(int(time.time()))
                }
                reservation_detail_key = f"reservation_detail:{reservation_id}:{sku}"
                pipe.setex(reservation_detail_key, self.RESERVATION_TIMEOUT, json.dumps(reservation_data))
            
            # Execute all Redis operations atomically
            pipe.execute()
            
            logger.info(f"Stock reserved successfully for reservation {reservation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error reserving stock for reservation {reservation_id}: {e}")
            return False
    
    def release_reservation(self, items: List[Dict], facility_id: str, reservation_id: str) -> bool:
        """
        Release reserved stock (rollback reservation)
        
        Args:
            items: List of items with 'sku' and 'quantity' keys
            facility_id: Facility ID
            reservation_id: Reservation identifier to release
            
        Returns:
            bool: True if release successful
        """
        try:
            redis_client = self.get_redis_client()
            pipe = redis_client.pipeline()
            
            for item in items:
                sku = item['sku']
                quantity = item['quantity']
                reserved_key = self._get_reserved_key(sku, facility_id)
                
                # Decrease reserved stock
                pipe.decrby(reserved_key, quantity)
                
                # Remove reservation detail
                reservation_detail_key = f"reservation_detail:{reservation_id}:{sku}"
                pipe.delete(reservation_detail_key)
            
            pipe.execute()
            
            logger.info(f"Stock reservation released for reservation {reservation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error releasing stock reservation {reservation_id}: {e}")
            return False
    
    def confirm_reservation(self, items: List[Dict], facility_id: str, reservation_id: str) -> bool:
        """
        Confirm reservation and deduct from actual stock
        
        Args:
            items: List of items with 'sku' and 'quantity' keys
            facility_id: Facility ID
            reservation_id: Reservation identifier to confirm
            
        Returns:
            bool: True if confirmation successful
        """
        try:
            redis_client = self.get_redis_client()
            pipe = redis_client.pipeline()
            
            for item in items:
                sku = item['sku']
                quantity = item['quantity']
                stock_key = self._get_stock_key(sku, facility_id)
                reserved_key = self._get_reserved_key(sku, facility_id)
                
                # Deduct from actual stock
                pipe.decrby(stock_key, quantity)
                # Deduct from reserved stock
                pipe.decrby(reserved_key, quantity)
                
                # Remove reservation detail
                reservation_detail_key = f"reservation_detail:{reservation_id}:{sku}"
                pipe.delete(reservation_detail_key)
            
            pipe.execute()
            
            logger.info(f"Stock reservation confirmed and deducted for reservation {reservation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error confirming stock reservation {reservation_id}: {e}")
            return False
    
    def set_stock(self, sku: str, facility_id: str, quantity: int) -> bool:
        """
        Set stock quantity for a SKU at a facility (for testing/admin purposes)
        
        Args:
            sku: Product SKU
            facility_id: Facility ID
            quantity: Stock quantity to set
            
        Returns:
            bool: True if successful
        """
        try:
            redis_client = self.get_redis_client()
            stock_key = self._get_stock_key(sku, facility_id)
            
            redis_client.set(stock_key, quantity)
            
            logger.info(f"Stock set for SKU {sku} at facility {facility_id}: {quantity}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting stock for SKU {sku} at facility {facility_id}: {e}")
            return False

# Import time module for timestamp
import time
