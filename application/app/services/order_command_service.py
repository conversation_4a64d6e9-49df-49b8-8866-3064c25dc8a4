from typing import Dict, Optional
import logging
import random
import string
from datetime import datetime
from .stock_service import StockService

logger = logging.getLogger(__name__)

def generate_random_prefix() -> str:
    """Generate a random 4-character alphanumeric string for order ID prefix.

    Returns:
        str: A 4-character string containing uppercase letters and digits (e.g., 'A2K4', '489K', 'X7B9')
    """
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=4))

class OrderCommandService:
    """Service for handling order commands (Create, Update, Cancel) with single database"""

    def __init__(self, db_conn=None):
        self.db_conn = db_conn
        self.stock_service = StockService()

    async def create_order(self, order_data: Dict, db_conn=None) -> Dict:
        """Create order with Redis stock verification - Only creates order if stock is available"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for create operation")

        try:
            logger.info(f"Creating order for customer {order_data['customer_id']}")

            # Validate required fields
            required_fields = ['customer_id', 'customer_name', 'facility_id', 'facility_name', 'status', 'total_amount']
            for field in required_fields:
                if field not in order_data or not order_data[field]:
                    raise ValueError(f"Missing required field: {field}")

            # STEP 1: Check stock availability in Redis
            if not order_data.get('items'):
                raise ValueError("Order must contain at least one item")

            facility_id = order_data['facility_id']
            items = order_data['items']

            logger.info(f"Checking stock availability for {len(items)} items at facility {facility_id}")

            # Check if all items are available in stock
            is_available, unavailable_items = self.stock_service.check_stock_availability(items, facility_id)

            if not is_available:
                logger.warning(f"Stock check failed for order. Unavailable items: {unavailable_items}")
                return {
                    "success": False,
                    "message": "Insufficient stock for one or more items",
                    "unavailable_items": unavailable_items,
                    "error_code": "INSUFFICIENT_STOCK"
                }

            # STEP 2: Generate order ID for reservation
            random_prefix = generate_random_prefix()
            # We'll use a temporary order ID for reservation, then get the actual one from DB
            temp_order_id = f"TEMP_{random_prefix}_{int(datetime.now().timestamp())}"

            # STEP 3: Reserve stock in Redis
            logger.info(f"Reserving stock for order {temp_order_id}")
            reservation_success = self.stock_service.reserve_stock(items, facility_id, temp_order_id)

            if not reservation_success:
                logger.error(f"Failed to reserve stock for order {temp_order_id}")
                return {
                    "success": False,
                    "message": "Failed to reserve stock. Please try again.",
                    "error_code": "RESERVATION_FAILED"
                }

            # STEP 4: Create order in database with transaction
            try:
                with conn.cursor() as cursor:
                    try:
                        # Calculate ETA (24 hours from now as default)
                        from datetime import datetime, timedelta
                        eta = datetime.now() + timedelta(hours=24)

                        # Insert order - order_id will be auto-generated by PostgreSQL
                        order_query = """
                            INSERT INTO orders (
                                random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                            RETURNING order_id, id
                        """

                        order_values = (
                            random_prefix,
                            order_data['customer_id'],
                            order_data['customer_name'],
                            order_data['facility_id'],
                            order_data['facility_name'],
                            order_data['status'],
                            order_data['total_amount'],
                            eta
                        )

                        cursor.execute(order_query, order_values)
                        result_row = cursor.fetchone()
                        generated_order_id = result_row[0]

                        # Insert order items
                        item_query = """
                            INSERT INTO order_items (
                                order_id, sku, quantity, unit_price, sale_price, status
                            ) VALUES (%s, %s, %s, %s, %s, %s)
                        """

                        for item in items:
                            item_values = (
                                generated_order_id,
                                item['sku'],
                                item['quantity'],
                                item['unit_price'],
                                item['sale_price'],
                                order_data['status']  # Set item status same as order status
                            )
                            cursor.execute(item_query, item_values)

                        # Commit database transaction
                        conn.commit()

                        # STEP 5: Confirm stock reservation (deduct from actual stock)
                        logger.info(f"Confirming stock reservation for order {generated_order_id}")
                        confirmation_success = self.stock_service.confirm_reservation(items, facility_id, temp_order_id)

                        if not confirmation_success:
                            logger.error(f"Failed to confirm stock reservation for order {generated_order_id}")
                            # This is a critical error - order is created but stock not deducted
                            # In production, you might want to implement compensation logic here

                        logger.info(f"Order {generated_order_id} created successfully with stock verification")

                        return {
                            "success": True,
                            "message": "Order created successfully with stock verification",
                            "order_id": generated_order_id,
                            "eta": eta.isoformat(),
                            "stock_reserved": confirmation_success
                        }

                    except Exception as db_error:
                        # Database error - rollback DB transaction and release stock reservation
                        conn.rollback()
                        logger.error(f"Database error creating order: {db_error}")

                        # Release stock reservation
                        logger.info(f"Releasing stock reservation due to database error")
                        self.stock_service.release_reservation(items, facility_id, temp_order_id)

                        raise

            except Exception as order_error:
                # Any other error during order creation - release stock reservation
                logger.error(f"Error during order creation: {order_error}")

                # Release stock reservation
                logger.info(f"Releasing stock reservation due to order creation error")
                self.stock_service.release_reservation(items, facility_id, temp_order_id)

                raise

        except Exception as e:
            logger.error(f"Failed to create order: {e}")
            raise

    async def update_order_status(self, order_id: str, status: str, db_conn=None) -> Dict:
        """Update order status in both orders and order_items tables"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Update order status
                cursor.execute("""
                    UPDATE orders
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                # Update order items status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                conn.commit()
                logger.info(f"Order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Order status updated to {status}",
                    "order_id": order_id
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update order status {order_id}: {e}")
            raise

    async def update_item_status(self, order_id: str, sku: str, status: str, db_conn=None) -> Dict:
        """Update status of a specific item within an order"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Check if item exists in the order
                cursor.execute("""
                    SELECT id FROM order_items
                    WHERE order_id = %s AND sku = %s
                """, (order_id, sku))

                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }

                # Update specific item status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s AND sku = %s
                """, (status, order_id, sku))

                conn.commit()
                logger.info(f"Item {sku} in order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Item '{sku}' status updated to '{status}'",
                    "order_id": order_id,
                    "sku": sku,
                    "status": status
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update item status {order_id}/{sku}: {e}")
            raise
