from typing import Dict, Optional
import logging
from database import get_db  # Ensure database connection is handled

logger = logging.getLogger(__name__)

class OrderQueryService:
    """Service for handling order queries (Read operations) from single database"""

    def __init__(self, db_conn):
        self.db_conn = db_conn

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get single order by order_id from database with complete details"""
        if not self.db_conn:
            raise ValueError("Database is required for queries")

        with self.db_conn.cursor() as cursor:
            query = """
                SELECT o.id, o.order_id, o.customer_id, o.customer_name, o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta, o.created_at, o.updated_at,
                       array_agg(
                           CASE WHEN oi.id IS NOT NULL THEN
                               jsonb_build_object(
                                   'sku', oi.sku,
                                   'quantity', oi.quantity,
                                   'unit_price', oi.unit_price,
                                   'sale_price', oi.sale_price,
                                   'status', oi.status
                               )
                           END
                       ) as items
                FROM orders o
                LEFT JOIN order_items oi ON o.order_id = oi.order_id
                WHERE o.order_id = %s
                GROUP BY o.id, o.order_id, o.customer_id, o.customer_name, o.facility_id, o.facility_name,
                         o.status, o.total_amount, o.eta, o.created_at, o.updated_at
            """
            cursor.execute(query, (order_id,))
            row = cursor.fetchone()
            if not row:
                return None
            return {
                "id": row[0],
                "order_id": row[1],
                "customer_id": row[2],
                "customer_name": row[3],
                "facility_id": row[4],
                "facility_name": row[5],
                "status": row[6],
                "total_amount": float(row[7]),
                "eta": row[8],
                "created_at": row[9],
                "updated_at": row[10],
                "items": [item for item in row[11] if item]
            }
