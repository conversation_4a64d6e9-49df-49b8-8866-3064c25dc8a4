from fastapi import Request
from fastapi.responses import JSONResponse
from firebase_admin import auth
from starlette.middleware.base import BaseHTTPMiddleware
import os
import logging

logger = logging.getLogger(__name__)

class FirebaseAuthMiddleware(BaseHTTPMiddleware):
    """Validate Firebase ID tokens and attach user info to `request.state`."""

    def __init__(self, app, excluded_paths: list[str] | None = None):
        super().__init__(app)
        # Add stock management endpoints to excluded paths for testing
        default_excluded = ["/health", "/docs", "/openapi.json", "/stock/health"]
        self.excluded_paths = excluded_paths or default_excluded

        # Check if we should skip Firebase auth for testing
        self.skip_auth = os.getenv("SKIP_FIREBASE_AUTH", "false").lower() == "true"
        if self.skip_auth:
            logger.warning("Firebase authentication is DISABLED - for testing only!")

    async def dispatch(self, request: Request, call_next):
        # Skip auth for health or other excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)

        # Skip auth for OPTIONS requests (preflight requests)
        if request.method == "OPTIONS":
            return await call_next(request)

        # Skip auth entirely if configured for testing
        if self.skip_auth:
            # Set a default user for testing - validation will be skipped in testing mode
            request.state.user_id = "test-user-123"
            request.state.phone_number = "+**********"
            return await call_next(request)

        auth_header = request.headers.get("authorization")
        if not auth_header:
            return JSONResponse(status_code=401, content={"detail": "Unauthorized"})

        # Verify the Firebase ID token
        try:
            decoded_token = auth.verify_id_token(auth_header)
            request.state.user_id = decoded_token.get("user_id")
            request.state.phone_number = decoded_token.get("phone_number")
        except Exception as e:
            logger.error(f"Firebase token verification failed: {e}")
            return JSONResponse(status_code=401, content={"detail": "Invalid token"})

        return await call_next(request)
