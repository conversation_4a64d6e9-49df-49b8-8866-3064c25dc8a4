from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.database import close_db_pool
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
import os
from dotenv import load_dotenv

from firebase_admin import credentials
import firebase_admin

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Initialize Firebase (optional for testing)
try:
    firebase_config_path = os.getenv("FIREBASE_CONFIG_PATH", "app/firebase-sample.json")
    cred = credentials.Certificate(firebase_config_path)
    firebase_admin.initialize_app(cred)
    logger.info(f"Firebase initialized with config: {firebase_config_path}")
except Exception as e:
    logger.warning(f"Firebase initialization failed: {e}")
    logger.warning("Continuing without Firebase authentication")

@asynccontextmanager
async def lifespan(_: FastAPI):
    logger.info("Starting Rozana OMS")
    yield
    logger.info("Shutting down Rozana OMS")
    close_db_pool()

app = FastAPI(title="Rozana OMS", version="4.0.0", lifespan=lifespan)

allowed_origins = os.getenv("ALLOWED_ORIGINS")
if allowed_origins:
   origins = [origin.strip() for origin in allowed_origins.split(",")]
else:
   origins = ["*"]

logger.info(f"Configuring CORS with allowed origins: {origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from app.middlewares.firebase_auth import FirebaseAuthMiddleware
app.add_middleware(FirebaseAuthMiddleware)

from app.routes.order_create import router
from app.routes.health import router as health_router
from app.routes.order_updates import router as update_router
from app.routes.stock_management import router as stock_router

app.include_router(router)
app.include_router(health_router, tags=["health"])
app.include_router(update_router, tags=["order_updates"])
app.include_router(stock_router, tags=["stock_management"])
