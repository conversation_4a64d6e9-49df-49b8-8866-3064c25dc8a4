import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"

def test_stock_integration():
    """Test the complete stock integration flow"""
    
    print("🧪 Testing Stock Integration with Order Management")
    print("=" * 60)
    
    # Step 1: Set up initial stock
    print("\n1️⃣ Setting up initial stock...")
    
    stock_data = {
        "facility_id": "FAC-001",
        "items": [
            {"sku": "ITEM-001", "quantity": 10},
            {"sku": "ITEM-002", "quantity": 5},
            {"sku": "ITEM-003", "quantity": 0}  # Out of stock item
        ]
    }
    
    response = requests.post(f"{BASE_URL}/stock/set", json=stock_data)
    print(f"Stock setup status: {response.status_code}")
    print(f"Response: {response.json()}")
    
    # Step 2: Check stock levels
    print("\n2️⃣ Checking current stock levels...")
    
    response = requests.get(f"{BASE_URL}/stock/facility/FAC-001")
    print(f"Stock check status: {response.status_code}")
    print(f"Current stock: {json.dumps(response.json(), indent=2)}")
    
    # Step 3: Test successful order creation (sufficient stock)
    print("\n3️⃣ Testing order creation with sufficient stock...")
    
    order_data_success = {
        "customer_id": "CUST-001",
        "customer_name": "John Doe",
        "facility_id": "FAC-001", 
        "facility_name": "Main Warehouse",
        "status": "pending",
        "total_amount": 149.98,
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 2,  # Available: 10
                "unit_price": 45.00,
                "sale_price": 49.99
            },
            {
                "sku": "ITEM-002", 
                "quantity": 1,  # Available: 5
                "unit_price": 45.00,
                "sale_price": 49.99
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/create_order", json=order_data_success)
    print(f"Order creation status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        order_id = response.json().get("order_id")
        print(f"✅ Order created successfully: {order_id}")
        
        # Check updated stock levels
        print("\n📊 Checking stock after successful order...")
        response = requests.get(f"{BASE_URL}/stock/facility/FAC-001")
        print(f"Updated stock: {json.dumps(response.json(), indent=2)}")
    
    # Step 4: Test order creation with insufficient stock
    print("\n4️⃣ Testing order creation with insufficient stock...")
    
    order_data_fail = {
        "customer_id": "CUST-002",
        "customer_name": "Jane Smith",
        "facility_id": "FAC-001", 
        "facility_name": "Main Warehouse",
        "status": "pending",
        "total_amount": 299.97,
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 20,  # Requesting more than available
                "unit_price": 45.00,
                "sale_price": 49.99
            },
            {
                "sku": "ITEM-003", 
                "quantity": 1,  # Out of stock item
                "unit_price": 45.00,
                "sale_price": 49.99
            }
        ]
    }
    
    response = requests.post(f"{BASE_URL}/create_order", json=order_data_fail)
    print(f"Order creation status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 400:
        print("✅ Order correctly rejected due to insufficient stock")
    
    # Step 5: Test stock availability check
    print("\n5️⃣ Testing stock availability check...")
    
    availability_check = {
        "facility_id": "FAC-001",
        "items": [
            {"sku": "ITEM-001", "quantity": 5},
            {"sku": "ITEM-002", "quantity": 10},  # More than available
            {"sku": "ITEM-003", "quantity": 1}   # Out of stock
        ]
    }
    
    response = requests.post(f"{BASE_URL}/stock/check-availability", json=availability_check)
    print(f"Availability check status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    # Step 6: Test individual stock check
    print("\n6️⃣ Testing individual stock check...")
    
    response = requests.get(f"{BASE_URL}/stock/check?sku=ITEM-001&facility_id=FAC-001")
    print(f"Individual stock check status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    # Step 7: Test stock service health
    print("\n7️⃣ Testing stock service health...")
    
    response = requests.get(f"{BASE_URL}/stock/health")
    print(f"Health check status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    print("\n🎉 Stock integration test completed!")
    print("=" * 60)

if __name__ == "__main__":
    try:
        test_stock_integration()
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
