from fastapi import APIRouter, HTTPException
import logging
from app.database import get_db
from app.services import OrderCommandService
from app.models.orders import OrderStatusUpdate, OrderItemStatusUpdate

router = APIRouter()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@router.put("/update_order_status")
@router.patch("/update_order_status")
async def update_order_status(order_update: OrderStatusUpdate):
    """Updates order status in both orders and order_items tables"""
    async with get_db() as db_conn:
        try:
            service = OrderCommandService()
            result = await service.update_order_status(order_update.order_id, order_update.status, db_conn)

            if not result.get("success", False):
                raise HTTPException(status_code=404, detail=result.get("message", "Order not found"))

            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating order status {order_update.order_id}: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/update_item_status")
@router.patch("/update_item_status")
async def update_item_status(item_update: OrderItemStatusUpdate):
    """Updates status of a specific item within an order"""
    async with get_db() as db_conn:
        try:
            service = OrderCommandService()
            result = await service.update_item_status(item_update.order_id, item_update.sku, item_update.status, db_conn)

            if not result.get("success", False):
                raise HTTPException(status_code=404, detail=result.get("message", "Order or item not found"))

            return result
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating item status {item_update.order_id}/{item_update.sku}: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
