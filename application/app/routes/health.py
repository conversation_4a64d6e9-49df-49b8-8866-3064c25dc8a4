from fastapi import APIRouter, HTTPException, Request

router = APIRouter()

@router.get("/health")
async def health_check(request: Request):
    user_id = request.scope.get('user_id', 'N/A')
    phone_number = request.scope.get('phone_number', 'N/A')
    print(user_id, phone_number)
    return {"status": "healthy", "version": "4.0.0", "service": "rozana-oms", "user_id": user_id, "phone_number": phone_number}