from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
import logging
from app.services.stock_service import StockService

router = APIRouter()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models for stock management
class StockItem(BaseModel):
    sku: str = Field(..., min_length=1, max_length=100)
    quantity: int = Field(..., ge=0)

class StockSetRequest(BaseModel):
    facility_id: str = Field(..., min_length=1, max_length=50)
    items: List[StockItem]

class StockCheckRequest(BaseModel):
    facility_id: str = Field(..., min_length=1, max_length=50)
    items: List[StockItem]

class StockResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None

class BulkStockSetRequest(BaseModel):
    facility_id: str = Field(..., min_length=1, max_length=50)
    items: List[StockItem]

# Stock Management Endpoints

@router.post("/stock/set", response_model=StockResponse)
async def set_stock(request: StockSetRequest):
    """
    Set stock quantities for multiple SKUs at a facility
    This is typically used by inventory management systems
    """
    try:
        stock_service = StockService()
        
        success_count = 0
        failed_items = []
        
        for item in request.items:
            try:
                success = stock_service.set_stock(item.sku, request.facility_id, item.quantity)
                if success:
                    success_count += 1
                else:
                    failed_items.append(item.sku)
            except Exception as e:
                logger.error(f"Error setting stock for SKU {item.sku}: {e}")
                failed_items.append(item.sku)
        
        if failed_items:
            return StockResponse(
                success=False,
                message=f"Failed to set stock for {len(failed_items)} items",
                data={
                    "success_count": success_count,
                    "failed_items": failed_items
                }
            )
        
        return StockResponse(
            success=True,
            message=f"Stock set successfully for {success_count} items",
            data={"success_count": success_count}
        )
        
    except Exception as e:
        logger.error(f"Error in set_stock endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/stock/check")
async def check_stock(
    sku: str = Query(..., description="Product SKU"),
    facility_id: str = Query(..., description="Facility ID")
):
    """
    Check available stock for a specific SKU at a facility
    """
    try:
        stock_service = StockService()
        
        available_stock = stock_service.get_available_stock(sku, facility_id)
        
        return {
            "success": True,
            "sku": sku,
            "facility_id": facility_id,
            "available_stock": available_stock
        }
        
    except Exception as e:
        logger.error(f"Error checking stock for SKU {sku} at facility {facility_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/stock/check-availability", response_model=StockResponse)
async def check_stock_availability(request: StockCheckRequest):
    """
    Check stock availability for multiple items
    Returns detailed information about available vs requested quantities
    """
    try:
        stock_service = StockService()
        
        is_available, unavailable_items = stock_service.check_stock_availability(
            [item.dict() for item in request.items], 
            request.facility_id
        )
        
        # Get detailed stock info for all items
        stock_details = []
        for item in request.items:
            available_qty = stock_service.get_available_stock(item.sku, request.facility_id)
            stock_details.append({
                "sku": item.sku,
                "requested_quantity": item.quantity,
                "available_quantity": available_qty,
                "is_sufficient": available_qty >= item.quantity
            })
        
        return StockResponse(
            success=is_available,
            message="Stock availability check completed",
            data={
                "all_items_available": is_available,
                "stock_details": stock_details,
                "unavailable_items": unavailable_items if not is_available else []
            }
        )
        
    except Exception as e:
        logger.error(f"Error in check_stock_availability endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/stock/facility/{facility_id}")
async def get_facility_stock(
    facility_id: str,
    skus: Optional[str] = Query(None, description="Comma-separated list of SKUs to check")
):
    """
    Get stock information for all or specific SKUs at a facility
    """
    try:
        stock_service = StockService()
        redis_client = stock_service.get_redis_client()
        
        if skus:
            # Check specific SKUs
            sku_list = [sku.strip() for sku in skus.split(",")]
            stock_info = {}
            
            for sku in sku_list:
                try:
                    available_stock = stock_service.get_available_stock(sku, facility_id)
                    stock_info[sku] = {
                        "available_stock": available_stock,
                        "status": "available" if available_stock > 0 else "out_of_stock"
                    }
                except Exception as e:
                    stock_info[sku] = {
                        "available_stock": 0,
                        "status": "error",
                        "error": str(e)
                    }
        else:
            # Get all stock for the facility
            stock_pattern = f"{stock_service.STOCK_KEY_PREFIX}{facility_id}:*"
            stock_keys = redis_client.keys(stock_pattern)
            
            stock_info = {}
            for key in stock_keys:
                # Extract SKU from key
                sku = key.replace(f"{stock_service.STOCK_KEY_PREFIX}{facility_id}:", "")
                try:
                    available_stock = stock_service.get_available_stock(sku, facility_id)
                    stock_info[sku] = {
                        "available_stock": available_stock,
                        "status": "available" if available_stock > 0 else "out_of_stock"
                    }
                except Exception as e:
                    stock_info[sku] = {
                        "available_stock": 0,
                        "status": "error",
                        "error": str(e)
                    }
        
        return {
            "success": True,
            "facility_id": facility_id,
            "stock_count": len(stock_info),
            "stock_info": stock_info
        }
        
    except Exception as e:
        logger.error(f"Error getting facility stock for {facility_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Health check for Redis connection
@router.get("/stock/health")
async def stock_service_health():
    """
    Check if Redis stock service is healthy
    """
    try:
        stock_service = StockService()
        redis_client = stock_service.get_redis_client()
        
        # Test Redis connection
        redis_client.ping()
        
        return {
            "success": True,
            "message": "Stock service is healthy",
            "redis_status": "connected"
        }
        
    except Exception as e:
        logger.error(f"Stock service health check failed: {e}")
        raise HTTPException(
            status_code=503, 
            detail=f"Stock service unavailable: {str(e)}"
        )

# =========================
# INVENTORY ENDPOINTS
# =========================

@router.get("/inventory/get_stock")
async def inventory_get_stock(
    sku: str = Query(..., description="Product SKU, e.g. 'PHONE-001'"),
    facility_id: str = Query("facility_001", description="Facility ID (default: facility_001)")
):
    """
    Get available stock for a specific SKU at a facility.
    Example: /inventory/get_stock?sku=PHONE-001
    """
    try:
        stock_service = StockService()
        available_stock = stock_service.get_available_stock(sku, facility_id)
        return {
            "success": True,
            "sku": sku,
            "facility_id": facility_id,
            "available_stock": available_stock,
            "status": "available" if available_stock > 0 else "out_of_stock"
        }
    except Exception as e:
        logger.error(f"Error getting stock for SKU {sku} at facility {facility_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/inventory/set_stock", response_model=StockResponse)
async def inventory_set_stock(request: StockSetRequest):
    """
    Set stock for one or more SKUs at a facility.
    Example body:
    {
        "facility_id": "facility_001",
        "items": [
            {"sku": "PHONE-001", "quantity": 100},
            {"sku": "PHONE-002", "quantity": 50}
        ]
    }
    """
    try:
        stock_service = StockService()
        success_count = 0
        failed_items = []
        for item in request.items:
            try:
                success = stock_service.set_stock(item.sku, request.facility_id, item.quantity)
                if success:
                    success_count += 1
                else:
                    failed_items.append(item.sku)
            except Exception as e:
                logger.error(f"Error setting stock for SKU {item.sku}: {e}")
                failed_items.append(item.sku)
        if failed_items:
            return StockResponse(
                success=False,
                message=f"Failed to set stock for {len(failed_items)} items",
                data={"success_count": success_count, "failed_items": failed_items}
            )
        return StockResponse(
            success=True,
            message=f"Stock set successfully for {success_count} items",
            data={"success_count": success_count}
        )
    except Exception as e:
        logger.error(f"Error in inventory_set_stock endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/inventory/bulk_set_stock", response_model=StockResponse)
async def inventory_bulk_set_stock(request: BulkStockSetRequest):
    """
    Bulk set stock for multiple SKUs at a facility. Same as set_stock but for high-volume operations.
    Example body:
    {
        "facility_id": "facility_001",
        "items": [
            {"sku": "PHONE-001", "quantity": 100},
            {"sku": "PHONE-002", "quantity": 50}
        ]
    }
    """
    try:
        stock_service = StockService()
        success_count = 0
        failed_items = []
        for item in request.items:
            try:
                success = stock_service.set_stock(item.sku, request.facility_id, item.quantity)
                if success:
                    success_count += 1
                else:
                    failed_items.append(item.sku)
            except Exception as e:
                logger.error(f"Error setting bulk stock for SKU {item.sku}: {e}")
                failed_items.append(item.sku)
        if failed_items:
            return StockResponse(
                success=False,
                message=f"Failed to set stock for {len(failed_items)} items",
                data={"success_count": success_count, "failed_items": failed_items}
            )
        return StockResponse(
            success=True,
            message=f"Bulk stock set successfully for {success_count} items",
            data={"success_count": success_count}
        )
    except Exception as e:
        logger.error(f"Error in inventory_bulk_set_stock endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
